require('esbuild')
    .build({
        entryPoints: ['./src/main.ts'],
        bundle: true,
        outfile: 'dist/bundle.js',
        format: 'esm',
        platform: 'neutral',
        minify: false,
        target: ['es2020'],
        mainFields: ['main', 'module'],
        loader: {
            '.json': 'json',
        },
        external: [
            'class-transformer',
            'uuid-by-string',
        ],
    })
    .catch(() => process.exit(1))
