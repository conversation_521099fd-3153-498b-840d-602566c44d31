import { Input } from '../api/core/input/input'
import { AccountProfile, FingerprintDeviceGroup, ProxyGroup, getProfile } from '../accountProfile'
import { NpcPathFinder } from '../api/core/npcPathFinder'
import { BotScript } from '../api/core/script/botScript'
import { LineOfSight, WorldArea } from '../api/core/world/lineOfSight'
import { Bank } from '../api/game/bank'
import { Equipment } from '../api/game/equipment'
import { GameObjects } from '../api/game/gameObjects'
import { GrandExchange } from '../api/game/grandExchange'
import { GroundItems } from '../api/game/groundItems'
import { InputBox } from '../api/game/inputBox'
import { Inventory } from '../api/game/inventory'
import { Npcs } from '../api/game/npcs'
import { Varps } from '../api/game/varps'
import { Widgets } from '../api/game/widgets'
import { WorldToScreen } from '../api/game/worldToScreen'
import { Point } from '../api/model/point'
import { Tile } from '../api/model/tile'
import { BehavioralPointGenerator } from '../api/utils/behavior-fingerprinting/behavioralPointGenerator'
import { getCache, getOrSetCache, putCache } from '../api/utils/cache'
import { java } from '../api/utils/javaCommonClasses'
import { PersistentLogs } from '../api/utils/persistentLogs'
import { Random } from '../api/utils/random'
import { javaPerform, log, toJavaColor } from '../api/utils/utils'
import { Actor } from '../api/wrappers/actor'
import { Client } from '../api/wrappers/client'
import { Game } from '../api/wrappers/game'
import { GameObject } from '../api/wrappers/gameObject'
import { Menu } from '../api/wrappers/menu'
import { Player } from '../api/wrappers/player'
import { ScriptActiveTimeMonitor } from './background-tasks/scriptActiveTimeMonitor'
import { Bot } from './bot'
import { ProxyHandler } from './proxyHandler'
import { BotSettings } from '../botSettings'
import { FingerprintSpoofer } from './background-tasks/fingerprintSpoofer'
import { drawLine, drawLineFromScreenPoint, drawRect, drawRectNoConvert, drawText } from './canvasHandler'
import { Ui } from './ui'
import { Dialogue } from '../api/game/dialogue'
import { SceneTilesContainer } from '../api/wrappers/sceneTilesContainer'
import { isPartiallyEmittedExpression } from 'frida-compile/ext/typescript'
import { Scene } from '../api/wrappers/scene'
import { BehavioralSleep } from '../api/utils/behavior-fingerprinting/behavioralSleep'

export class Debuggers {
    // static isOn = false
    static isGameDebug = false
    static isNpcsDebug = false
    static isPlayersDebug = false
    static isGameObjectsDebug = false
    static isVarbitsDebug = false
    static isLineOfSightDebug = false
    static isInventoryDebug = false
    static isCollisionFlagsDebug = false
    static isDeviceInfoDebug = false
    static isPersistentLogsDebug = false
    static isFirebaseDebug = false
    static isMemoryDebug = false
    static isWidgetsDebug = false
    static isGroundItemsDebug = false

    static hardwareDataRequests = 0
    static lastAllocationCheck: { time: number, count: number } = null
    static memoryHistory: { time: number, appUsed: number, nativeHeap: number, allocCount: number }[] = []

    static canvas: any
    static paint: any
    static yOffset: any = 0
    static gameObject: GameObject

    static draw(canvas: any, paint: any) {
        const Style = Java.use('android.graphics.Paint$Style')
        const Color = Java.use('android.graphics.Color')

        this.canvas = canvas
        this.paint = paint
        this.yOffset = BotScript.currentTextY

        Debuggers.drawTextNewLine('Sleep: ' + BehavioralSleep.sleepReason)
        Debuggers.drawTextNewLine('clickCount: ' + BehavioralSleep.getClickCount())
        Debuggers.drawTextNewLine('currentProxy: ' + ProxyHandler.currentProxy)

        this.drawMouse(canvas, paint)

        paint.setColor(Color.argb(255, 255, 255, 255))
        paint.setStyle(Style.FILL.value)

        if (this.isGameDebug) {
            this.gameDebug()
        }

        if (this.isNpcsDebug) {
            this.npcsDebug()
        }
        if (this.isVarbitsDebug) {
            this.varbitsDebug()
        }
        if (this.isLineOfSightDebug) {
            this.lineOfSightDebug()
        }
        if (this.isPlayersDebug) {
            this.playersDebug()
        }
        if (this.isGameObjectsDebug) {
            this.gameObjectsDebug()
        }
        if (this.isInventoryDebug) {
            this.inventoryDebug()
        }
        if (this.isCollisionFlagsDebug) {
            this.collisionFlagsDebug()
        }
        if (this.isDeviceInfoDebug) {
            this.deviceInfoDebug()
        }

        if (this.isPersistentLogsDebug) {
            this.persistentLogsDebug()
        }

        if (this.isFirebaseDebug) {
            this.firebaseDebug()
        }

        if (this.isMemoryDebug) {
            this.memoryDebug()
        }

        if (this.isWidgetsDebug) {
            this.widgetsDebug()
        }
        
        if (this.isGroundItemsDebug) {
            this.groundItemsDebug()
        }

        // Debuggers.drawTextNewLine(ProxyHandler.currentProxy + '/' + ProxyHandler.currentIpAddress)
    }

    static lineOfSightDebug() {
        // const player = Player.local
        // const localTile = player.tile.toLocal()

        // for (let x = -4; x < 4; x++) {
        //     for (let y = -4; y < 4; y++) {
        //         const tile = localTile.add(x, y)
        //         const minimus = Npcs.getById(12808)

        //         if (LineOfSight.actorHasLineOfSightTo(player, new WorldArea(tile.x, tile.y, tile.z, 1, 1), [minimus.tile.toLocal()], 2, 2)) {
        //             this.drawTile(tile, '')
        //         }
        //     }
        // }

        const jaguar = Npcs.get((n) => n.id == 12810 && n.animation != 10849)
        const shaman = Npcs.get((n) => n.id == 12811 && n.animation != 10860)

        if (!jaguar) return
        if (!shaman) return
        const localTile = jaguar.tile.toLocal()

        for (let x = -4; x < 4; x++) {
            for (let y = -4; y < 4; y++) {
                const tile = localTile.add(x, y)

                if (LineOfSight.actorHasLineOfSightTo(jaguar, new WorldArea(tile.x, tile.y, tile.z, 1, 1), [shaman.tile.toLocal()], 2, 2)) {
                    this.drawTile(tile, '')
                }
            }
        }
    }

    static varbitsDebug() {
        Debuggers.drawTextNewLine('Varbits debug on')
        Debuggers.drawTextNewLine('9650: ' + Varps.getVarbit(9650))
        Debuggers.drawTextNewLine('membershipDays: ' + Varps.membershipDays)
        Debuggers.drawTextNewLine('isAutoRetalitateOn: ' + Varps.isAutoRetalitateOn)

        const cached = getCache('varbits_debug')
        const cachedConfigs = getCache('configs_debug')

        const allVarbits: Map<number, number> = new Map()
        const allConfigs: Map<number, number> = new Map()

        for (let i = 0; i < 15000; i++) {
            allVarbits.set(i, Varps.getVarbit(i))
        }

        for (let i = 0; i < 5000; i++) {
            allConfigs.set(i, Varps.getConfig(i))
        }

        if (cached == null) {
            putCache('varbits_debug', allVarbits)
            putCache('configs_debug', allConfigs)
            return
        }

        for (const key of allVarbits.keys()) {
            if (key == 12391 || key == 12392) continue //they are spamming too much

            if (cached.get(key) != allVarbits.get(key)) {
                log(`[Varbit] ${key}  ${cached.get(key)} -> ${allVarbits.get(key)}`)
            }
        }

        for (const key of allConfigs.keys()) {
            if (key == 8843 || key == 8841 || key == 3079 || key == 3077) continue //they are spamming too much
            if (cachedConfigs.get(key) != allConfigs.get(key)) {
                log(`[Config] ${key}  ${cachedConfigs.get(key)} -> ${allConfigs.get(key)}`)
            }
        }

        putCache('varbits_debug', allVarbits)
        putCache('configs_debug', allConfigs)
    }

    static collisionFlagsDebug() {
        Debuggers.drawTextNewLine('0: ' + Client.collisionFlags.getAt(Player.local.localX, Player.local.localY, Player.local.tile.z))
        Debuggers.drawTextNewLine('1: ' + Client.collisionFlags.getAt(Player.local.localX + 1, Player.local.localY, Player.local.tile.z))
        Debuggers.drawTextNewLine('2: ' + Client.collisionFlags.getAt(Player.local.localX, Player.local.localY + 1, Player.local.tile.z))

        Debuggers.drawTextNewLine('3: ' + Client.collisionFlags.getAt(Player.local.localX - 1, Player.local.localY, Player.local.tile.z))
        Debuggers.drawTextNewLine('4: ' + Client.collisionFlags.getAt(Player.local.localX, Player.local.localY - 1, Player.local.tile.z))

        // this.drawTile(Player.local.tile, '' + Client.collisionFlags.getAt(Player.local.localX, Player.local.localY, Player.local.tile.z))

        this.drawTextNewLine('Walkable north' + new Tile(Player.local.tile.x, Player.local.tile.y + 1, Player.local?.tile.z).isReachable())

        const sceneTile = Client.sceneTilesData.sceneTilesContainer.tileAt(Player.local.localX, Player.local.localY + 1)
        this.drawTextNewLine('sceneTile: ' + sceneTile.base + ' ' + sceneTile.wallObject?.id + ' ' + sceneTile.groundObject?.id + ' ' + sceneTile.standardGameObject?.id + ' ' + sceneTile.wallDecorationObject?.id)
        this.drawTextNewLine('sceneTile: ' + sceneTile.base + ' ' + sceneTile.wallObject?.tag + ' ' + sceneTile.groundObject?.tag + ' ' + sceneTile.standardGameObject?.tag + ' ' + sceneTile.wallDecorationObject?.tag)
        this.drawTextNewLine('sceneTile: ' + sceneTile.base + ' ' + sceneTile.wallObject?.base + ' ' + sceneTile.groundObject?.base + ' ' + sceneTile.standardGameObject?.base + ' ' + sceneTile.wallDecorationObject?.base)

    }

    static inventoryDebug() {
        Debuggers.drawTextNewLine(
            'Equipment: ' +
            Equipment.get()
                .items.map((i) => i.id)
                .join(',')
        )

        Debuggers.drawTextNewLine(
            'Inventory: ' +
            Inventory.get()
                .items.map((i) => i.id)
                .join(',')
        )

        Debuggers.drawTextNewLine('Items: ' + Inventory.get().items.length)
        Debuggers.drawTextNewLine('Ui size: ' + JSON.stringify(Ui.getScreenSize()))
        Debuggers.drawTextNewLine('withdrawAmount ' + Bank.withdrawAmount)

        Debuggers.drawTextNewLine('x1: ' + Client.get().add(0x68).readInt())
        Debuggers.drawTextNewLine('y1: ' + Client.get().add(0x6c).readInt())


        Debuggers.drawTextNewLine('x2: ' + Client.playableAreaX)
        Debuggers.drawTextNewLine('y2: ' + Client.playableAreaY)


        Debuggers.drawTextNewLine('x3: ' + Client.get().add(0x1fc4).readInt())
        Debuggers.drawTextNewLine('y3: ' + Client.get().add(0x1fc8).readInt())



        Debuggers.drawTextNewLine('x3: ' + Client.get().add(0x1fcc).readInt())
        Debuggers.drawTextNewLine('y3: ' + Client.get().add(0x1fd0).readInt())


        Debuggers.drawTextNewLine('x4: ' + Client.get().add(0x80).readPointer().add(0x30).readInt())
        Debuggers.drawTextNewLine('y4: ' + Client.get().add(0x80).readPointer().add(0x34).readInt())



        Debuggers.drawTextNewLine('x5: ' + Client.get().add(0x88).readPointer().add(0x10).readInt())
        Debuggers.drawTextNewLine('y5: ' + Client.get().add(0x88).readPointer().add(0x14).readInt())

        Debuggers.drawTextNewLine('xScaleFromGame: ' + xScaleFromGame())
        Debuggers.drawTextNewLine('yScaleFromGame: ' + yScaleFromGame())



        if (Bank.isOpen()) {
            Debuggers.drawTextNewLine('Bank items: ' + Bank.get().items.length)
        }


        this.paint.setStyle(java.Style.STROKE.value)


        for (const item of Inventory.get().items) {
            const x = item.widgetChild?.screenX
            const y = item.widgetChild?.screenY
            const width = item.widgetChild?.width
            const height = item.widgetChild?.height
            drawRect(this.canvas, x, y, x + width, y + height, this.paint)
        }

        this.paint.setColor(toJavaColor('#ffffff'))
        drawRect(this.canvas, 300, 300, 310, 310, this.paint)
        drawRect(this.canvas, 500, 300, 510, 310, this.paint)
        drawRect(this.canvas, 700, 300, 710, 310, this.paint)
        drawRect(this.canvas, 900, 300, 910, 310, this.paint)


        this.paint.setColor(toJavaColor('#ff00ff'))
        drawRectNoConvert(this.canvas, 300, 400, 310, 410, this.paint)
        drawRectNoConvert(this.canvas, 500, 400, 510, 410, this.paint)
        drawRectNoConvert(this.canvas, 700, 400, 710, 410, this.paint)
        drawRectNoConvert(this.canvas, 900, 400, 910, 410, this.paint)

        drawRectNoConvert(this.canvas, 1500, 400, 1510, 410, this.paint)
        drawRectNoConvert(this.canvas, 1700, 400, 1710, 410, this.paint)
        drawRectNoConvert(this.canvas, 1900, 400, 1910, 410, this.paint)

        drawRectNoConvert(this.canvas, 2200, 400, 2210, 410, this.paint)

        drawRectNoConvert(this.canvas, 2580, 400, 2590, 410, this.paint)


        const testIds = [786451, 786456, 786476]
        for (const id of testIds) {
            const w = Widgets.get(id)
            if (w) {

                Debuggers.drawTextNewLine("parentId: " + w.parentId)
                Debuggers.drawTextNewLine("screenX: " + w.screenX)
                Debuggers.drawTextNewLine("screenY: " + w.screenY)
                Debuggers.drawTextNewLine("relativeX: " + w.relativeX)
                Debuggers.drawTextNewLine("relativeY: " + w.relativeY)

                drawRect(this.canvas, w.screenX, w.screenY, w.screenX + w.width, w.screenY + w.height, this.paint)
            }
        }
    }

    static gameObjectsDebug() {
        // const tile = Client.sceneTilesData.sceneTilesContainer.tileAt(Player.local.localX, Player.local.localY)
        // log(tile.base, tile.wallDecorationObject, tile.wallDecorationObject?.realId)

        // for (const obj of GameObjects.getAll((p) => p.definition?.actions?.find((a) => a != null) != null, Player.local.tile, 3)) {
        for (const obj of GameObjects.getAll((p) =>true, Player.local.tile, 2)) {
            this.drawGameObject(obj, `${obj.name} (id: ${obj.id}) ${obj.gameObjectType} ${obj.base}`)
            this.drawTextNewLine(`${obj.name} (id: ${obj.id}, real id: ${obj.realId}) ${obj.gameObjectType} compositin: ${Game.getObjectComposition(obj.id).transformIds?.getAt(0)}`)
        }
    }
    static drawGameObject(obj: GameObject, text: string) {
        const Style = Java.use('android.graphics.Paint$Style')
        this.paint.setStyle(Style.FILL.value)
        this.paint.setColor(toJavaColor('#5cff64'))

        const width = obj.definition?.sizeX ?? 1
        const height = obj.definition?.sizeY ?? 1
        const tile = obj.tile
        const tiles = []
        for (let x = 0; x < width; x++) {
            for (let y = 0; y < height; y++) {
                tiles.push(tile.add(x, y))
            }
        }

        const centerX =
            tiles
                .map((t) => t.toAnimable())
                .map((t) => t.x)
                .reduce((a, b) => a + b) / tiles.length

        const centerY =
            tiles
                .map((t) => t.toAnimable())
                .map((t) => t.y)
                .reduce((a, b) => a + b) / tiles.length

        const wts = WorldToScreen.worldToScreen(centerX, centerY)
        const textWidth = this.paint.measureText(text)
        this.canvas.drawText(text, wts.x - textWidth / 2, wts.y, this.paint)
    }

    static playersDebug() {
        Debuggers.drawTextNewLine('Local: ' + Client.localPlayer?.base)
        Debuggers.drawTextNewLine('Tile: ' + Player.local.tile + ' index: ' + Player.local.index + ' combatLevel: ' + Player.local.combatLevel)
        Debuggers.drawTextNewLine('isMoving: ' + Player.local.isMoving)
        Debuggers.drawTextNewLine('animation: ' + Player.local.animation)
        Debuggers.drawTextNewLine('interactingIndex: ' + Player.local.interactingIndex)
        Debuggers.drawTextNewLine( ' username: ' + Player.local.username )

        Debuggers.drawTextNewLine('Players hashmap size: ' + Client.worldView.playersHashMap.size)
        Debuggers.drawTextNewLine('Local tile: ' + Player.local.localX + ' ' + Player.local.localY + 'headIconPrayer: ' + Player.local.headIconPrayer + 'headIconSkull: ' + Player.local.headIconSkull)

        for (const n of Client.players /*.filter((n) => n.tile.distance() < 15)*/) {
            Debuggers.drawTextNewLine('Player: ' + n.username + "  " + n.index + " | "+ n.localX + ", " + n.localY)
            this.drawActor(
                n,
                `(anim: ${n.animation} ->  base: ${n.base}) <br> headIcon: ${n.headIconSkull} headIconPrayer: ${n.headIconPrayer} index: ${n.index} combatLevel: ${n.combatLevel}`
            )
        }

        this.paint.setTextSize(14)
    }

    static npcsDebug() {
        Debuggers.drawTextNewLine('Npcs debug on')
        Debuggers.drawTextNewLine('All npcs: ' + Npcs.getAll().length)
        
        // for (const n of Npcs.getAll().filter((n) => n.tile.distance() < 15)) {
        for (const n of Npcs.getAll().filter((n) => n.tile.distance() < 15)) {
            Debuggers.drawTextNewLine('Npc: ' + n.base + " " + "" + n.name + " " + n.id)
            // Debuggers.drawTextNewLine('Npc: ' + n.name + ' id: ' + n.id + ' exists: ' + n.exists() + ' index: ' + n.index + ' cycle ' + n.renderCycle)
            this.drawActor(n, `${n.name} (id: ${n.composite?.id} -> ${n.transformedId}, hp: ${n.currentHitpoints}) <br> ( index: ${n.index}, basee: ${n.base})`)
        }

        // const npc = Npcs.get((n) => n.interactingIndex == Player.local.index)
        // if (npc) {
        //     const path = new NpcPathFinder().findPath(npc.tile, Player.local.tile)

        //     Debuggers.drawTextNewLine('Path: ' + path.length)

        //     path.forEach((t) => {
        //         this.drawTile(t, '')
        //     })
        // }

        this.paint.setTextSize(14)
    }

    static deviceInfoDebug() {
        const Build = Java.use('android.os.Build')
        const profile = getProfile()

        Debuggers.drawTextNewLine('Device Information:')
        Debuggers.drawTextNewLine('libArchitecture: ' + Game.clientFingerprint.libArchitecture)
        Debuggers.drawTextNewLine('processorArchitecture: ' + Game.clientFingerprint.processorArchitecture)
        Debuggers.drawTextNewLine('deviceName: ' + Game.clientFingerprint.deviceInfo.deviceName)
        Debuggers.drawTextNewLine('Build.FINGERPRINT: ' + Build.FINGERPRINT.value)
        Debuggers.drawTextNewLine('Build.PRODUCT: ' + Build.PRODUCT.value)
        Debuggers.drawTextNewLine('Build.MANUFACTURER: ' + Build.MANUFACTURER.value)
        Debuggers.drawTextNewLine('Build.HARDWARE: ' + Build.HARDWARE.value)
        Debuggers.drawTextNewLine('Build.MODEL: ' + Build.MODEL.value)

        if (profile) {
            Debuggers.drawTextNewLine('Account Profile:')
            Debuggers.drawTextNewLine('Proxy Group: ' + ProxyGroup[profile.proxyGroup])
            Debuggers.drawTextNewLine('Device Group: ' + FingerprintDeviceGroup[profile.spoofDeviceGroup])
            Debuggers.drawTextNewLine('Mule Index: ' + profile.muleIndex)
        }
        Debuggers.drawTextNewLine('Screen size: ' + JSON.stringify(Ui.getScreenSize()))
        Debuggers.drawTextNewLine('Proxy mode: ' + (BotSettings.useProxyInjector ? 'injector' : 'legacy'))
        Debuggers.drawTextNewLine('Proxy: ' + ProxyHandler.currentProxy)
    }

    static persistentLogsDebug() {
        Debuggers.drawTextNewLine('Persistent Logs Debug')

        // Get all logs
        const allLogs = PersistentLogs.readLines()

        // Display the last 30 logs (or all if less than 30)
        const logsToDisplay = allLogs.length > 30
            ? allLogs.slice(allLogs.length - 30)
            : allLogs

        // Display log count
        Debuggers.drawTextNewLine(`Showing ${logsToDisplay.length} of ${allLogs.length} logs`)

        // Display a separator
        Debuggers.drawTextNewLine('-----------------------------------')

        // Display each log entry
        for (const logEntry of logsToDisplay) {
            Debuggers.drawTextNewLine(logEntry)
        }
    }

    static firebaseDebug() {


    }

    static memoryDebug() {
        Debuggers.drawTextNewLine('Memory Debug')
        Debuggers.drawTextNewLine('-----------------------------------')

        // Application memory information
        let usedMemory = 0
        let nativeHeapAllocated = 0

        try {
            // Get application memory info
            javaPerform(() => {
                const Runtime = Java.use('java.lang.Runtime')
                const runtime = Runtime.getRuntime()

                const maxMemory = runtime.maxMemory() / (1024 * 1024)
                const totalMemory = runtime.totalMemory() / (1024 * 1024)
                const freeMemory = runtime.freeMemory() / (1024 * 1024)
                usedMemory = totalMemory - freeMemory

                const fridaHeapSize = Frida.heapSize  / (1024 * 1024)

                Debuggers.drawTextNewLine(`App Max Memory: ${maxMemory.toFixed(2)} MB`)
                Debuggers.drawTextNewLine(`App Total Memory: ${totalMemory.toFixed(2)} MB`)
                Debuggers.drawTextNewLine(`App Used Memory: ${usedMemory.toFixed(2)} MB (${(usedMemory/totalMemory*100).toFixed(1)}%)`)
                Debuggers.drawTextNewLine(`App Free Memory: ${freeMemory.toFixed(2)} MB`)
                Debuggers.drawTextNewLine(`Frida heap size: ${fridaHeapSize.toFixed(2)} MB`)
            })
        } catch (e) {
            Debuggers.drawTextNewLine(`Error getting app memory: ${e}`)
        }

        Debuggers.drawTextNewLine('-----------------------------------')


        // Bot allocations information
        Debuggers.drawTextNewLine(`Bot Allocations Count: ${Bot.allocations.length}`)

        // Calculate total size of allocations
        let totalAllocationSize = 0
        try {
            for (const allocation of Bot.allocations) {
                // Try to estimate size - this is approximate
                if (allocation && !allocation.isNull()) {
                    // Assume each allocation is at least 8 bytes
                    totalAllocationSize += 8
                }
            }

            const totalAllocationMB = totalAllocationSize / (1024 * 1024)
            Debuggers.drawTextNewLine(`Bot Allocations Size (est.): ${totalAllocationMB.toFixed(2)} MB`)
        } catch (e) {
            Debuggers.drawTextNewLine(`Error calculating allocation size: ${e}`)
        }

        // Memory leak detection
        if (Bot.allocations.length > 1000) {
            Debuggers.drawTextNewLine(`WARNING: High allocation count detected!`)
        }

        // Track allocation growth over time
        const currentTime = Date.now()
        const currentCount = Bot.allocations.length

        if (!Debuggers.lastAllocationCheck) {
            Debuggers.lastAllocationCheck = { time: currentTime, count: currentCount }
        } else if (currentTime - Debuggers.lastAllocationCheck.time > 60000) { // Check every minute
            const growthRate = (currentCount - Debuggers.lastAllocationCheck.count) / ((currentTime - Debuggers.lastAllocationCheck.time) / 60000)
            Debuggers.drawTextNewLine(`Allocation Growth Rate: ${growthRate.toFixed(2)} per minute`)

            // Update last check
            Debuggers.lastAllocationCheck = { time: currentTime, count: currentCount }

            // Alert if growth rate is high
            if (growthRate > 100) {
                Debuggers.drawTextNewLine(`WARNING: High allocation growth rate detected!`)
            }
        }
    }

    static widgetsDebug() {

        const inputWidget = Widgets.getByRootId(162, 43)

        if (inputWidget) {
            Debuggers.drawTextNewLine('Input widget: ' + inputWidget.id + ' ' + inputWidget.base)
            Debuggers.drawTextNewLine(' text: ' + inputWidget.text)
            Debuggers.drawTextNewLine(' isInput: ' + inputWidget.isInput)
        }

        const runEnergyWidget = Widgets.getByRootId(897, 24)

        if (runEnergyWidget) {
            Debuggers.drawTextNewLine('' + runEnergyWidget.id + ' ' + runEnergyWidget.base)
            Debuggers.drawTextNewLine('text '  + runEnergyWidget.text)
            Debuggers.drawTextNewLine('cycle '  + runEnergyWidget.cycle)
            Debuggers.drawTextNewLine('isHidden '  + runEnergyWidget.isHidden)
            Debuggers.drawTextNewLine('isInput '  + runEnergyWidget.isInput)
            Debuggers.drawTextNewLine('contentType ' + runEnergyWidget.contentType)
            Debuggers.drawTextNewLine('isRendered ' + runEnergyWidget.isRendered)
            Debuggers.drawTextNewLine('relativeX ' + runEnergyWidget.relativeX)
            Debuggers.drawTextNewLine('relativeY ' + runEnergyWidget.relativeY)
            Debuggers.drawTextNewLine('width'  + runEnergyWidget.width)
            Debuggers.drawTextNewLine('height'  + runEnergyWidget.height)
            Debuggers.drawTextNewLine('screenX ' + runEnergyWidget.screenX)
            Debuggers.drawTextNewLine('screenY ' + runEnergyWidget.screenY)

        }

             const geItemsWidget = Widgets.getByRootId(162, 51)?.children
             if(geItemsWidget) {
                 Debuggers.drawTextNewLine(' getChildrenCount ' +geItemsWidget.getChildrenCount())
                   for (let i = 0; i < geItemsWidget.getChildrenCount(); i++) {
                       var widget = geItemsWidget.getAt(i)
                       Debuggers.drawTextNewLine(widget.base + " " + widget.itemId)
                   }
    }
        return false

    }

    static gameDebug() {
        Debuggers.drawTextNewLine('Login message 1: ' + Game.loginMessage1 + ' ' + Game.loginMessage2 + ' ' + Game.loginMessage3)
        Debuggers.drawTextNewLine('loginUsername/pass ' + Game.loginUsername + ' ' + Game.loginPassword)

        Debuggers.drawTextNewLine('Client: ' + Client.get() + ' Game: ' + Game.base)
        Debuggers.drawTextNewLine('Destination: ' + Client.destinationX + ' ' + Client.destinationY + " " + Scene.destination.distance())
        Debuggers.drawTextNewLine('Cycle: loggedIn: ' + Client.loggedInCycle + ' | main: ' + Client.cycle + ' | server: ' + Client.serverTick + ' | time: ' + Bot.lastGameCycle)
        Debuggers.drawTextNewLine('Login screen: ' + Game.loginScreenId)
        Debuggers.drawTextNewLine('Game.state ' + Client.gameState)
        Debuggers.drawTextNewLine('Plane: ' + Client.plane)
        Debuggers.drawTextNewLine('World: ' + Client.currentWorld)
        Debuggers.drawTextNewLine('scene:' + Client.scene?.base)
        Debuggers.drawTextNewLine('sceneTilesData:' + Client.sceneTilesData?.base)
        Debuggers.drawTextNewLine('sceneTilesContainer:' + Client.sceneTilesData.sceneTilesContainer?.base)
        Debuggers.drawTextNewLine('scenecoords:' + Client.worldView.sceneCoords?.base)

        Debuggers.drawTextNewLine(
            'Camera: [x: ' + Client.cameraX + ' y: ' + Client.cameraY + ' z: ' + Client.cameraZ + ' pitch: ' + Client.cameraPitch + ' yaw: ' + Client.cameraYaw + ', zoom: ' + Client.cameraZoom + ']'
        )
        Debuggers.drawTextNewLine('Cutscene: ' + Varps.cutscene)

        Debuggers.drawTextNewLine('Dialogue: ' + Dialogue.isOpen())
        Debuggers.drawTextNewLine('Menu: isOpem: ' + Menu.isOpen)
        Debuggers.drawTextNewLine('Game state: ' + Client.gameState)
        Debuggers.drawTextNewLine('Run energy: ' + Player.getRunEnergy())
        Debuggers.drawTextNewLine('Offers: ' + JSON.stringify(GrandExchange.offers.map((o) => o.itemId)))
        Debuggers.drawTextNewLine(`Camera: [x: ${Client.cameraX} y: ${Client.cameraY} z: ${Client.cameraZ} pitch: ${Client.cameraPitch} yaw: ${Client.cameraYaw}, zoom: ${Client.cameraZoom}]`)
        Debuggers.drawTextNewLine('Input: ' + InputBox.isOpen)
        Debuggers.drawTextNewLine('Input text: ' + InputBox.text)
        Debuggers.drawTextNewLine('Input?: ' + Widgets.getByRootId(162, 42)?.isInput)
        Debuggers.drawTextNewLine('Input?: ' + Widgets.getByRootId(162, 42)?.isRendered)

        // Debuggers.drawTextNewLine('rootWidgetCount: ' + Client.rootWidgetCount)

        Debuggers.drawTextNewLine('Worldview:' + Client.worldView.base)
        Debuggers.drawTextNewLine('Base:' + Client.scene?.baseX + ' ' + Client.scene?.baseY)
        Debuggers.drawTextNewLine('Scene:' + Client.scene.base + ' instanced: ' + Client.scene.isInstancedArea)
        Debuggers.drawTextNewLine('Deposit all cycle:' + Widgets.get(786476)?.cycle)
        Debuggers.drawTextNewLine('Withdraw Amount ' + Bank.withdrawAmount)
        Debuggers.drawTextNewLine('Running texture ' + Widgets.getByRootId(897, 28)?.textureId)

        Debuggers.drawTextNewLine(
            'Hint arrow: ' +
            Client.hintArrowContainer.hintArrow.hintArrowX +
            ' ' +
            Client.hintArrowContainer.hintArrow.hintArrowX +
            ' type: ' +
            Client.hintArrowContainer.hintArrow.hintArrowType +
            ' npc: ' +
            Client.hintArrowContainer.hintArrow.hintArrowNpcIndex
        )

        // Debuggers.drawTextNewLine(
        //     `Player: username: ${Player.local?.username} tile: ${Player.local?.tile} anim: ${Player.local?.animation} -> ${Player.local?.animationFrame} r: ${Player.local.renderX},${Player.local.renderY} ${Player.local.base}`
        // )
        // Debuggers.drawTextNewLine('Local tile: ' + Player.local.localX + ' ' + Player.local.localY)

        // const widget = Widgets.get(786476)
        // if (widget != null) {
        //     Debuggers.drawTextNewLine(
        //         'Widget id:' +
        //             widget.id +
        //             ' base: ' +
        //             widget.base +
        //             ' screenX: ' +
        //             widget.screenX +
        //             ' screenY: ' +
        //             widget.screenY +
        //             ' contentType: ' +
        //             widget.contentType +
        //             ' text: ' +
        //             widget.text +
        //             ' cycle: ' +
        //             widget.cycle
        //     )
        // }
    }

    static gen1: Point[] = []

    static drawMouse(canvas: any, paint: any) {
        const Style = Java.use('android.graphics.Paint$Style')
        const Color = Java.use('android.graphics.Color')

        Input.currentMousePosition.convertToScreenPoint()
        const x = Input.currentMousePosition.x
        const y = Input.currentMousePosition.y

        // if (this.isGameDebug) {
            Debuggers.drawTextNewLine(`(${x}, ${y})`)
            paint.setColor(Color.argb(60, 255, 255, 0))
            Input.clickHistory.forEach((p) => {
                drawRect(canvas, p.x, p.y, p.x + 1, p.y + 1, paint)
            })
        // }

        if (x == -1 && y == -1) return

        paint.setColor(Color.YELLOW.value)
        drawLineFromScreenPoint(canvas, x - 7, y - 7, x + 7, y + 7, paint)
        drawLineFromScreenPoint(canvas, x + 7, y - 7, x - 7, y + 7, paint)
    }

    static drawActor(actor: Actor, text: string = null) {
        const Style = Java.use('android.graphics.Paint$Style')
        const Color = Java.use('android.graphics.Color')

        const center = WorldToScreen.worldToScreen(actor.renderX, actor.renderY)

        this.paint.setStyle(Style.FILL.value)
        this.paint.setColor(toJavaColor('#5cff64'))

        if (text != null) {
            const splits = text.split('<br>')

            let y = 0
            for (const split of splits) {
                this.paint.setTextSize(Ui.textSizeRegular)

                const textWidth = this.paint.measureText(split)
                drawText(this.canvas, split, center.x - textWidth / 2, center.y + y, this.paint)
                y += Ui.textSizeRegular + 2
            }
        }
    }

    static drawTile(tile: Tile, text: string = null) {
        const Style = Java.use('android.graphics.Paint$Style')
        const Color = Java.use('android.graphics.Color')

        const poly = WorldToScreen.getTilePolygon(tile)

        this.paint.setColor(toJavaColor('#00470444'))

        this.paint.setStyle(Style.FILL.value)

        drawLine(this.canvas, poly.points[0].x, poly.points[0].y, poly.points[1].x, poly.points[1].y, this.paint)
        drawLine(this.canvas, poly.points[0].x, poly.points[0].y, poly.points[2].x, poly.points[2].y, this.paint)
        drawLine(this.canvas, poly.points[2].x, poly.points[2].y, poly.points[3].x, poly.points[3].y, this.paint)
        drawLine(this.canvas, poly.points[3].x, poly.points[3].y, poly.points[1].x, poly.points[1].y, this.paint)

        this.paint.setColor(toJavaColor('#5cff64'))

        if (text != null) {
            const center = poly.getCenterPoint()

            const splits = text.split('<br>')

            let y = 0
            for (const split of splits) {
                this.paint.setTextSize(12)

                const textWidth = this.paint.measureText(split)
                drawText(this.canvas, split, center.x - textWidth / 2, center.y + y, this.paint)
                y += 13
            }
        }
    }

    static createClicksProfile() {
        BehavioralPointGenerator.cache.clear()
        const generator = BehavioralPointGenerator.get('test3' + Random.next(1000000), 500, 300, 35, 35)

        generator.getPatternPoints()

        this.gen1 = [] //generator.points;// []

        for (var i = 0; i < 28; i++) {
            this.gen1.push(generator.getPoint())
        }
    }

    static drawClicks(canvas: any, paint: any) {
        const Style = Java.use('android.graphics.Paint$Style')
        const Color = Java.use('android.graphics.Color')

        for (let entry of Array.from(BehavioralPointGenerator.cache.entries())) {
            const value = entry[1]
            // paint.setStyle(Style.STROKE.value);
            paint.setColor(Color.argb(150, 1, 1, 0))
            drawRect(canvas, value._rectX, value._rectY, value._rectX + value._rectWidth, value._rectY + value._rectHeight, paint)
        }

        paint.setStyle(Style.FILL.value)
        paint.setColor(Color.argb(100, 0, 1, 0))

        Debuggers.gen1.forEach((p) => {
            drawRect(canvas, p.x, p.y, p.x + 1, p.y + 1, paint)
        })
    }

    static drawTextNewLine(str: string) {
        this.paint.setTextSize(Ui.textSizeRegular)
        this.canvas.drawText(str, 120, this.yOffset, this.paint)
        this.yOffset += Ui.textSizeRegular
    }

    static groundItemsDebug() {
       
        Debuggers.drawTextNewLine('Ground Items Debug')
        Debuggers.drawTextNewLine('-----------------------------------')
        const localTile = Player.local.tile.toLocalTile()
        Debuggers.drawTextNewLine("scenetile: " + Client.sceneTilesData.sceneTilesContainer.tileAt(localTile.x, localTile.y, 0).base)

        // const getSceneTileAtFunc = new NativeFunction(Game.base.add("0x479970"), "pointer", ["pointer", "int", "int", "int"])
        // const sceneTile = getSceneTileAtFunc(Client.sceneTilesData.sceneTilesContainer.base, localTile.x, localTile.y, 0)
        // Debuggers.drawTextNewLine("sceneTile(20): " + sceneTile)

        const item = GroundItems.getNearest(Player.local.tile, p => true, 2)
        Debuggers.drawTextNewLine("item.x: " + item.x)
        Debuggers.drawTextNewLine("item.y: " + item.y)
        Debuggers.drawTextNewLine("item.base: " + item.base)
        Debuggers.drawTextNewLine("item.id: " + item.id)
       
       
       
       
       
        
    }
}
