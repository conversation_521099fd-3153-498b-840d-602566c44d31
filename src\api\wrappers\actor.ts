import { Offsets } from '../../offsets'
import { Tile, TileType } from '../model/tile'
import { Client } from './client'
import { SpotAnimation } from './spotAnimation'
import { Wrapper } from './wrapper'

export class Actor extends Wrapper {
    constructor(baseAddress: NativePointer) {
        super(baseAddress)
    }

    get isMoving() {
        return this.base.add(Offsets.Actor.isMoving).readInt() > 0
    }

    get interactingIndex() {
        const interactIndex = this.base.add(Offsets.Actor.interactingIndex).readInt()
        if (interactIndex > 65536) {
            return interactIndex - 65536
        }
        return interactIndex
    }

    get localX() {
        return this.base.add(Offsets.Actor.localX).readS8()
    }

    get localY() {
        return this.base.add(Offsets.Actor.localY).readS8()
    }

    get renderX() {
        return this.base.add(Offsets.Actor.renderX).readInt()
    }

    get renderY() {
        return this.base.add(Offsets.Actor.renderY).readInt()
    }

    get animation() {
        return this.base.add(Offsets.Actor.animation).readInt()
    }
    // get animationFrame() {
    //     return this.base.add(Offsets.Actor.animationFrame).readInt()
    // }
    // get isRunningSpotanim() {
    //     return this.base.add(Offsets.Actor.isRunningSpotanim).readInt()
    // }
    //get spotAnimation() {
    //    return new SpotAnimation(this.base.add(Offsets.Actor.spotAnimation).readPointer()).orNull()
    //}

    get rotation() {
        return this.base.add(Offsets.Actor.rotation).readInt()
    }

    get tile() {
        return new Tile(this.localX, this.localY, Client.plane, TileType.LOCAL).toWorld()
    }

    get isAnimating() {
        return this.animation != -1
    }
}
