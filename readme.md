npm run essbuild
adb -s 83CVB19C16000683
adb -s 83CVB19C16000683 push C:\Users\<USER>\Desktop\GoldfarmData\fridabot\dist/bundle.js /sdcard/Android/data/com.jagex.oldscape.android/files/bundle.js

adb tcpip 5555
adb connect **************:5555

//poco
adb connect **************


for x64:
adb -s localhost:16384 forward tcp:27042 tcp:27042
adb connect localhost:16384

rev 222:
0x17e480 - loadRegion? or something like that. Could be used to get scene.isInstanced(0x54dd)

rev 223:
//Actor.localX, localY
00121867 rdx*14 = *(int32*t*)((char*)r12 + 0x1b0);
0012186f rcx_23 = *(int32_t*)((char*)r12 + 0x1d8);

rev 226:

    sceneTile.groundObject
    -   liblibs.hal.system.osclient.so+17301E
    -   liblibs.hal.system.osclient.so+27D16A
    -   liblibs.hal.system.osclient.so+27D174
    -   liblibs.hal.system.osclient.so+439627
    -   liblibs.hal.system.osclient.so+439627
    -   liblibs.hal.system.osclient.so+43962E
    -   liblibs.hal.system.osclient.so+27B719
    -   liblibs.hal.system.osclient.so+45CC41

    groundObject.tag
    -   liblibs.hal.system.osclient.so+439545
    -   liblibs.hal.system.osclient.so+4395FD

rev 227:
sub_27cd30 ADD HITSPLAT
sub_27d000 ADD HEALTH BAR
sub_27f7c0 false
sub_3b3750 false






DESKTOP:


npc.rendercycle 231.1 x86
liblibs.hal.system.osclient.so+484AB8:
liblibs.hal.system.osclient.so+484E2D:
liblibs.hal.system.osclient.so+484F11:


actor.rotation 231.1 x86
liblibs.hal.system.osclient.so+2A2007:

widget.itemId 231.1 x86
29684B
2A81CC


actor.interasectingindex 231.1 x86
liblibs.hal.system.osclient.so+26FA5A:



actor.ismoving  231.1 x86
liblibs.hal.system.osclient.so+25C09F
liblibs.hal.system.osclient.so+26F488:


actor.animation 232.1 x86 
liblibs.hal.system.osclient.so+2A73FE

player.username 232.1 x86  00165f4e


SceneTilesContainer tileAtXarr, y z, 232.1 x86  
0048861d



GameObjectComposite transformIds 232.1 x86
0F 95 C1 0F 94 C2