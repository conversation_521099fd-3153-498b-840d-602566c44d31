import { State } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { GameObjects } from '../../../api/game/gameObjects'
import { Inventory } from '../../../api/game/inventory'
import { Skill } from '../../../api/game/skill'
import { Walking } from '../../../api/game/walking'
import { Tile } from '../../../api/model/tile'
import { Time } from '../../../api/utils/time'
import { Player } from '../../../api/wrappers/player'
import { ItemId } from '../../../data/itemId'
import { Teleport } from '../../../data/teleport'
import { ThieveHuman } from './thieveHuman'
import { ThieverGe } from './thieverGe'

export class ThieveStall extends State {
    depositedUnusedItems: any
    stallTile: Tile = new Tile(3268, 3410, 0)
    stallId: number = 635

    onAction(): void {
        Walking.setRunAuto()

        if (Skill.THIEVING.getCurrentLevel() >= 55) {
            this.setState(new ThieveHuman(99))
            return
        }

        if (!this.updateSpot()) {
            return
        }

        if (Inventory.getFreeSlots() <= 0) {
            this.dropInventory()
            return
        }

        if (!Walking.walkTo(this.stallTile, 1)) {
            return
        }

        const stall = GameObjects.getById(this.stallId, Player.local.tile, 3)
        if (stall == null) {
            this.dropInventory()
            return
        }

        stall?.click(4)
        Time.sleep(() => Player.local.isAnimating)
        Time.sleep(100, 300)
    }

    updateSpot() {
        if (Skill.THIEVING.getCurrentLevel() < 25) {
            this.stallTile = new Tile(3268, 3410, 0)
            this.stallId = 635
            return true
        }

        this.stallTile = new Tile(1796, 3607, 0)
        this.stallId = 28823

        if (this.stallTile.distance() > 120) {
            if (
                !Withdraw.builder().predicate(Teleport.skillsNecklacePredicate).amount(1).minimumAmount(1).ensureSpace().orState(ThieverGe).withdraw() ||
                !Withdraw.builder().predicate(Teleport.ringOfWealthPredicate).amount(1).minimumAmount(1).ensureSpace().orState(ThieverGe).withdraw()
            ) {
                return false
            }
        }

        return true
    }

    dropInventory() {
        const toDrop = Inventory.get().getAllByPredicate((i) => [9030, 9038, 9042, 1978, 1980, 1935, 1951, 5504, 1955, 247, 464, 1963, 2114, 2120, 19653, 2102, 5972].includes(i.id))

        if (toDrop.length == 0 && Inventory.getFreeSlots() <= 0) {
            if (!this.initDeposit()) {
                return
            }
        }

        for (const item of toDrop) {
            item.click(1007, 7)
            Time.sleep(150, 350)
        }
    }

    initDeposit() {
        if (!this.depositedUnusedItems && Inventory.get().getAllExceptIds(ItemId.CUP_OF_TEA).length > 0) {
            if (Inventory.getFreeSlots() >= 28) {
                this.depositedUnusedItems = true
                return true
            }

            if (Bank.openNearest()) {
                Bank.depositAll()
                return false
            }

            return false
        }

        return true
    }
}
