import { Offsets } from '../../offsets'
import { Varps } from '../game/varps'
import { getCache } from '../utils/cache'
import { Game } from './game'
import { RsArray } from './rsArray'
import { Wrapper } from './wrapper'

export class GameObjectComposite extends Wrapper {
    constructor(baseAddress: NativePointer) {
        super(baseAddress)
    }

    get id(): number {
        return this.base.add(Offsets.GameObjectComposite.id).readInt()
    }

    get transformIds(): RsArray<number> {
        return new RsArray(this.base.add(Offsets.GameObjectComposite.transformIds).readPointer()).orNull()
    }

    get transformVarbit(): number {
        return this.base.add(Offsets.GameObjectComposite.transformVarbit).readInt()
    }
    get transformVarp(): number {
        return this.base.add(Offsets.GameObjectComposite.transformVarp).readInt()
    }

    get transform(): GameObjectComposite {
        if (this.transformVarbit != -1) {
            const varbitValue = Varps.getVarbit(this.transformVarbit)
            if (varbitValue == null) return this
            if(this.transformIds == null) return this
            return Game.getObjectComposition(this.transformIds?.getAt(varbitValue))
        }

        if (this.transformVarp != -1) {
            const configValue = Varps.getConfig(this.transformVarp)
            if(this.transformIds == null) return this
            return Game.getObjectComposition(this.transformIds?.getAt(configValue))
        }

        return this
    }
}
