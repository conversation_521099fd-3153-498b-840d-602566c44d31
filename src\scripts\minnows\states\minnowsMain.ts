import { State, createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Dialogue } from '../../../api/game/dialogue'
import { GeAction } from '../../../api/game/geAction'
import { InputBox } from '../../../api/game/inputBox'
import { Inventory } from '../../../api/game/inventory'
import { Npcs } from '../../../api/game/npcs'
import { Walking } from '../../../api/game/walking'
import { WorldHopping } from '../../../api/game/worldHopping'
import { MenuOpcode } from '../../../api/model/menuOpcode'
import { DefaultGeActionsState } from '../../../api/script-utils/states/geStates'
import { Time } from '../../../api/utils/time'
import { log } from '../../../api/utils/utils'
import { Npc } from '../../../api/wrappers/npc'
import { Player } from '../../../api/wrappers/player'
import { BotSettings } from '../../../botSettings'
import { ItemId } from '../../../data/itemId'
import { ItemPredicate } from '../../../data/itemPredicates'
import { Locations } from '../../../data/locations'
import { Minnows } from '../minnows'

export class MinnowsMain extends State {
    SPOT_IDS = [7731, 7730, 7732, 7733]
    currentSpot: Npc
    askForPermit: boolean

    onBackgroundAction(): void {
        Walking.setRunAuto()
    }

    onAction(): void {
        if (!WorldHopping.switchToP2pExcept(BotSettings.tradeWorldP2p)) {
            return
        }

        if (!Inventory.containsAll(ItemId.SMALL_FISHING_NET)) {
            this.setState(this.doBanking)
            return
        }

        if (Locations.minnows.distance() > 100 && (!Inventory.containsByPredicate(ItemPredicate.skillsNecklace) || !Inventory.containsByPredicate(ItemPredicate.ringOfWealth))) {
            this.setState(this.doBanking)
            return
        }

        if (Locations.minnows.distance() > 15) {
            this.setState(this.walkToSpot)
            return
        }

        this.setState(this.fishMinnows)
    }

    isBigFishOnCurrentSpot() {
        return this.isBigFishOnSpot(this.currentSpot)
    }

    isBigFishOnSpot(npc: Npc) {
        //const spotanim = npc?.spotAnimation
        //`return spotanim != null && spotanim?.frame != 24
        throw new Error('spotanim Not implemented')
    }

    geState = new DefaultGeActionsState(
        this,
        Minnows.resupply,
        [GeAction.item(ItemId.SMALL_FISHING_NET, 1).gePrice(1.1, 3000), GeAction.item(ItemId.RING_OF_WEALTH_5, 2).gePrice(1.1, 3000), GeAction.item(ItemId.SKILLS_NECKLACE6, 2).gePrice(1.1, 3000)],
        () => {
            return true
        }
    )

    fishMinnows = createState('Fishing minnows', () => {
        if (Inventory.get().countByIds(ItemId.MINNOW) > 10000) {
            this.setState(this.exchangeFishes)
            return
        }
        if (Inventory.get().countWithNoted(ItemId.RAW_SHARK) > 1000) {
            this.setState(Minnows.giveToMule)
            return
        }

        if (Player.local.isAnimating && this.currentSpot?.tile?.preciseDistance(Player.local.tile) <= 1.0 && !this.isBigFishOnCurrentSpot()) {
            return
        }

        this.currentSpot = Npcs.get((npc) => this.SPOT_IDS.includes(npc.composite.id) && !this.isBigFishOnSpot(npc)) //todo spotAnimation 1387 handle

        if (this.currentSpot == null) {
            return
        }

        this.currentSpot.click(MenuOpcode.NPC_FIRST_OPTION)
        Time.sleep(1000)
        Time.sleep(() => Player.local.isAnimating)
    })

    doBanking = createState('Banking', () => {
        if (Inventory.getFreeSlots() < 10) {
            log('free slots:', Inventory.getFreeSlots())
            Bank.openNearest() && Bank.depositAll()
            return
        }

        if (
            !Withdraw.all(
                this.geState,
                Withdraw.id(ItemId.SMALL_FISHING_NET, 1).minimumAmount(1).exactAmount().ensureSpace(),
                Withdraw.predicate(ItemPredicate.skillsNecklace, 1).minimumAmount(1).exactAmount().ensureSpace(),
                Withdraw.predicate(ItemPredicate.ringOfWealth, 1).minimumAmount(1).exactAmount().ensureSpace()
            )
        ) {
            return
        }

        this.setState(this.walkToSpot)
    })

    exchangeFishes = createState('Exchange fishes', () => {
        if (Inventory.get().countByIds(ItemId.MINNOW) < 50) {
            this.setDefaultState()
            return
        }

        if (InputBox.isOpen) {
            InputBox.type(500000, 1000, true)
            Time.sleep(1000, 1500)
            return
        }

        if (Dialogue.isOpen()) {
            Dialogue.goNext()
            return
        }

        Npcs.getById(7735)?.click(MenuOpcode.NPC_THIRD_OPTION)

        if (Time.sleep(() => Dialogue.isOpen())) {
            Dialogue.goNext()
            Time.sleep(2000, () => InputBox.isOpen)
            return
        }
    })

    walkToSpot = createState('Walk to spot', () => {
        if (Locations.minnows.distance() < 10) {
            this.setDefaultState()
            return
        }

        if (Inventory.getFreeSlots() < 10) {
            this.setState(this.doBanking)

            return
        }

        if (Locations.minnows.distance() > 50 && !Inventory.containsByPredicate(ItemPredicate.skillsNecklace)) {
            this.setState(this.doBanking)
            return
        }

        if (this.askForPermit) {
            log('p1')
            if (Dialogue.contains('Have you got')) {
                this.askForPermit = false
                return
            }
            if (Dialogue.talkToNpcById(7735, true)) {
                Dialogue.goNext()
            }
            log('p3')
            return
        }

        if (Dialogue.isOpen()) {
            if (Dialogue.contains("I don't think I have")) {
                this.askForPermit = true
                return
            }
            Dialogue.goNext("Yes, let's trade") //TODO Showing anglers outfit
            return
        }

        if (Walking.walkTo(Locations.minnows, 10)) {
            this.setDefaultState()
            return
        }
    })
}
