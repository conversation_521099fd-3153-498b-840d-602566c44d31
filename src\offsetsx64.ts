export const Offsetsx64 = {
    Game: {
        loginScreenId: 0xc9c9c8,
        loginUsername: 0xc9ca68,
        loginPassword: 0xc9ca80,
        engineSettings: 0x2091908,
        worldSelectorWorlds: 0xc9c870,
        clientFingerprint: 0xc9c018,
        loginMessage1: 0xc9cc80,
        loginMessage2: 0xc9cc98,
        loginMessage3: 0xc9ccb0,
        client: 0xc9d1b0,
        widgets: 0x12e4e88,
        config: 0x12e4dd8,
        itemContainers: 0xc8d620,
        renderTiles: 0x48d550,
        onSoundEffect: 0x218100, //
        getTileHeight: 0x28cf90,
        getObjectComposition: 0x3caff0,
        setGameWorld: 0x1ffdd0,
        onClientTick: 0x250350,
        onRenderWidgets: 0x2b0600,
        onMessageAdded: 0xea5e0,
        forceDisconnect: 0x2cfbf0,
        onHardwareDataRequested: 0x86c270,
        addHealthBar: 0x2d9c00,
        invokeMenuActionHook: 0x2bbc50,
        writeJagString: 0xf5df0,
        // onCollisionFlagsOr: 0x35ca30,
        // onColliosnFlagsAnd: 0x35d120,
    },

    Client: {
        menu: 0x6f5f0,
        plane: 0x6b60, 
        currentWorld: 0x6ff54,
        gameState: 0x2130, //?
        loggedInCycle: 0x455974,
        cycle: 0x2154,
        serverTick: 0x2154 + 0x4,
        skillCurrentLevels: 0x6f42c,
        skillRealLevels: 0x6f490,
        skillExperiences: 0x6f4f4,
        localPlayer: 0x6f3f8,
        worldView: 0x6b30,
        sceneTilesData: 0x6b40,
        scene: 0x70a30,
        destinationTile: 0x2008,
        grandExchangeOffers: 0x6f7b0,
        hintArrowsDataContainer: 0x6ffb8, //
        cameraX: 0x6f8d8,
        cameraZ: 0x6f8dc,
        cameraY: 0x6f8e0,
        cameraPitch: 0x6f8e4,
        cameraYaw: 0x6f8e8,
        cameraZoom: 0x6f790,
        playableAreaX: 0x1fdc, //
        playableAreaY: 0x1fe0, //
    },
    
    GameObjectComposite: {  
        id: 0x2A, 
        transformIds: 0x1e8, 
        transformVarbit: 0x200, //89 7C 24 ? 4C 89 44 24 ? 49 8B ? ? 48 85 ? 0F 84
        transformVarp: 0x200 + 0x4, 
    },


    HintArrowsDataContainer: {
        instance: 0x0,
    },

    HintArrowsDataContainerInstance: {
        hintArrowType: 0x0,
        hintArrowNpcIndex: 0x4,
        hintArrowX: 0x10,
        hintArrowY: 0x14,
    },

    WorldView: {
        sceneCoords: 0x10,
        npcsHashMap: 0xb8,
        playersHashMap: 0x88,
    },

    Scene: {
        baseX: 0x54bc,
        baseY: 0x54bc + 0x4,
        isInstanced: 0x54dd,
    },
    SceneTile: {
        wallDecorationObject: 0x100,
        groundObject: 0x118,
        groundItems: 0xb0,
        standardGameObject: 0x68,
        wallObject: 0xf8, //
    },
    SceneTilesContainer: {
        tileAtXarr: 0x4e0,
        tileAtYarr: 0x4e4,
        tileAtZarr: 0x4f8,
        collisionFlags: 0x190, 
    },

    SceneTilesData: {
        sceneTilesContainer: 0x10,
    },

    SceneCoords: {
        selectedX: 0x3ac,
        selectedY: 0x3ac + 0x4,
    },

    Actor: {
        localX: 0x2a8, //
        localY: 0x2d0, //
        rotation: 0x150,  //
        isMoving: 0x2a4,  //
        interactingIndex: 0x26c, //
        animation: 0x398, // 
        renderX: 0x148, //
        renderY: 0x14c, //
        // spotAnimation: 0x3a8,
        // animationFrame: 0x16c + 0x4,
        // isRunningSpotanim: 0x2c,
    },
    SpotAnimation: {
        id: 0x4,
        frame: 0x14,
    },

    GroundObject: {
        tag: 0x30,
    },
    WallDecorationObject: {
        tag: 0x50, 
    },
    StandardGameObject: {
        tag: 0xd8, 
    },
    WallObject: {
        tag: 0x30, 
    },

    EngineSettings: {
        fpsCap: 0x185e0,
    },

    GrandExchangeOffer: {
        status: 0x0,
        itemId: 0x04,
        amount: 0x0c,
        price: 0x08,
        isBuying: 0x0c,
    },
    GroundItem: {
        id: 0x18,
        amount: 0x1c,
    },
    RsItemContainer: {
        id: 0x0,
        itemIds: 0x8,
        itemAmounts: 0x20,
    },
    Menu: {
        isOpen: 0x3a0,
    },
    Npc: {
        composite: 0x550,
        index: 0x558,
        renderCycle: 0x120,
    },
    Player: {
        username: 0x578,
        index: 0x5f0, 
        headIconPrayer: 0x594, 
        headIconSkull: 0x590,
        combatLevel: 0x5a0, 
    },

    NpcComposite: {
        id: 0x0,
    },

    Username: {
        value: 0x0,
    },
    Widget: {
        id: 0x24,
        index: 0x28,
        parentId: 0x94, 
        cycle: 0x58, 
        contentType: 0x50,
        text: 0x178, 
        itemId: 0x7a8 + 0x20, 
        itemAmount: 0x7ac + 0x20,
        width: 0x74, 
        height: 0x78, 
        relativeX: 0x7c, 
        relativeY: 0x80, 
        isHidden: 0x98, 
        textureId: 0xe8, 
        isInput: 0x5a0, 
        children: 0x7c8 + 0x10, 
    },

    ClientFingerprint: {
        libArchitecture: 0x0,
        processorArchitecture: 0x18,
        clientType: 0x50,
        deviceInfo: 0x68,
    },
    DeviceInfo: {
        deviceName: 0x0,
    },

    Native: {
        AInputEvent_getType: 0x00086850,
        AInputEvent_getDeviceId: 0x000877f0,

        AKeyEvent_getKeyCode: 0x00086840,
        AKeyEvent_getAction: 0x000877d0,
        AKeyEvent_getMetaState: 0x000877e0,

        AMotionEvent_getAction: 0x00095270,
        AMotionEvent_getPointerId: 0x000957f0,
        AMotionEvent_getX: 0x00094e20,
        AMotionEvent_getY: 0x00095200,
        AMotionEvent_getButtonState: 0x00094bf0,
        AMotionEvent_getPointerCount: 0x000943e0,
        AMotionEvent_getToolType: 0x00095540,
        AMotionEvent_getAxisValue: 0x000944b0,

        onInputEvent: 0xd8700,

        keyEvent_Action: 0x38,
        keyEvent_Flags: 0x3c,
        keyEvent_KeyCode: 0x40,
        keyEvent_ScanCode: 0x44,
        keyEvent_MetaState: 0x48,
        keyEvent_DownTime: 0x50,
        keyEvent_EventTime: 0x58,

        inputEvent_DeviceId: 0x4c,
        inputEvent_Type: 0x10,
        keyEvent_RepeatCount: 0x4c,
        inputEvent_Source: 0x10,
    },
}
