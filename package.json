{"name": "frida-bot-dev", "version": "1.0.0", "main": "main.js", "scripts": {"build": "node build-es.js", "dev": "adb kill-server && adb connect localhost:16384 && npm run dev-connect", "dev-connect": "npm run logcat", "push": "node ./tools/push.js", "push-full": "node ./tools/push.js --full", "deploy-local-hard": "npm run build && npm run push-local && npm run reload-bot-local", "deploy-local": "npm run build && npm run push-local", "debug": "python run_debug_script.py -y ", "debug-select": "python run_debug_script.py ", "deploy": "npm run build && npm run push", "test-prod": "npm run deploy-local && adb shell am force-stop com.jagex.oldscape.android && adb shell setprop debug.bot.launch_arg <EMAIL> && adb shell monkey -p com.jagex.oldscape.android -c android.intent.category.LAUNCHER 1", "watch": "webpack --watch", "prod-arm": "node build_prod.js --arm64", "prod": "node build_prod.js --x86_64", "deploy-arm": "npm run prod-arm64 && npm run push", "push-local": "adb push C:/Users/<USER>/Desktop/GoldfarmData/fridabot/dist/bundle.js /sdcard/Android/data/com.jagex.oldscape.android/files/bundle.js", "reload-bot-local": "adb  shell am force-stop com.jagex.oldscape.android && adb  shell monkey -p com.jagex.oldscape.android -c android.intent.category.LAUNCHER 1", "logcat": "powershell.exe -Command \"adb logcat -c | adb logcat | Select-String 'OSRSBOT'", "logcat-clean": "powershell.exe -Command \"adb logcat | Select-String -Pattern 'oldscape|OSRSBOT'\"", "pull-gamepack": "powershell.exe -ExecutionPolicy Bypass -File ./tools/pull_gamepack.ps1", "web-editor": "npm --prefix ./tools/web-nodes-editor run dev", "test-safe6h": "node test-safe6h.js", "build-webpack": "webpack --config webpack.config.js"}, "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/frida-gum": "^18.4.5", "@types/node": "^20.9.0", "esbuild": "^0.24.2", "prettier": "3.2.4", "terser-webpack-plugin": "^5.3.11", "ts-loader": "^9.5.1", "typescript": "^5.2.2", "webpack": "^5.101.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@types/memory-cache": "^0.2.5", "class-transformer": "^0.5.1", "frida-compile": "^16.4.1", "memory-cache": "^0.2.0", "random-item": "^4.0.1", "random-seedable": "^1.0.8", "seedrandom": "^3.0.5", "uuid-by-string": "^4.0.0", "windmouse": "^1.0.5"}}