export const OffsetsArm = {
    Game: {
        loginUsername: 0xaa6cb8,
        loginPassword: 0xaa71b0, 
        engineSettings: 0x1e97e70,
        worldSelectorWorlds: 0xaa7468, 
        clientFingerprint: 0xaa6368,
        loginScreenId: 0xaa6c90,
        loginMessage1: 0xaa6d18,
        loginMessage2: 0xaa6d30,
        loginMessage3: 0xaa6d48,
        client: 0xaa7448,
        widgets: 0x10f6008,
        config: 0x10f5fb0,
        itemContainers: 0xa7f338,
        renderTiles: 0x3bd0e0,
        onSoundEffect: 0x22a2f4,
        getTileHeight: 0x229d80,
        getObjectComposition: 0x31f45c,
        setGameWorld: 0x1bf858,
        onClientTick: 0x1f862c,
        onRenderWidgets: 0x244f0c,
        onMessageAdded: 0xf0348,
        forceDisconnect: 0x263488,
        onHardwareDataRequested: 0x6cd66c,
        addHealthBar: 0x26c11c,
        invokeMenuActionHook: 0x250670,
        writeJagString: 0xf8a60,
    },
     Client: {
        menu: 0x6f5e0,
        plane: 0x6b50, 
        currentWorld: 0x6ff34,
        gameState: 0x2130,
        loggedInCycle: 0x455784,
        cycle: 0x2154,
        serverTick: 0x2154 + 0x4,
        skillCurrentLevels: 0x6f41c,
        skillRealLevels: 0x6f480,
        skillExperiences: 0x6f4e4,
        localPlayer: 0x6f3e8,
        worldView: 0x6b20,
        sceneTilesData: 0x6b30,
        scene: 0x70a18,
        destinationTile: 0x2008,
        grandExchangeOffers: 0x6f798,
        hintArrowsDataContainer: 0x6ffb8,
        cameraX: 0x6f8c0,
        cameraZ: 0x6f8c4,
        cameraY: 0x6f8c8,
        cameraPitch: 0x6f8cc,
        cameraYaw: 0x6f8d0,
        cameraZoom: 0x6f778,
        playableAreaX: 0x1fdc,
        playableAreaY: 0x1fe0,
    },

    GameObjectComposite: {
        transformIds: 0x1f0,
    },

    HintArrowsDataContainer: {
        instance: 0x0,
    },

    HintArrowsDataContainerInstance: {
        hintArrowType: 0x0,
        hintArrowNpcIndex: 0x4,
        hintArrowX: 0x10,
        hintArrowY: 0x14,
    },

    WorldView: {
        sceneCoords: 0x10,
        npcsHashMap: 0xb8,
        playersHashMap: 0x88,
    },

    Scene: {
        baseX: 0x54bc,
        baseY: 0x54bc + 0x4,
        isInstanced: 0x54dd,
    },
    SceneTile: {
        wallDecorationObject: 0x100,
        groundObject: 0x118,
        groundItems: 0xb0,
        standardGameObject: 0x68,
        wallObject: 0xf8, //
    },
    SceneTilesContainer: {
        tileAtZarr: 0x4d8,
        tileAtXarr: 0x4c0,
        tileAtYarr: 0x4c4,
        collisionFlags: 0x1a0, //48 8D 0C 80 41 8B 6C C8 ? 29 D5
    },

    SceneTilesData: {
        sceneTilesContainer: 0x10,
    },

    SceneCoords: {
        selectedX: 0x38c,
        selectedY: 0x390,
    },

    Actor: {
        localX: 0x2d0, //
        localY: 0x2f8, //
        rotation: 0x148, //
        isMoving: 0x2cc, //
        interactingIndex: 0x264, // 
        animation: 0x290, //
        renderX: 0x140, //
        renderY: 0x144, //
        spotAnimation: 0x3a8,
        // animationFrame: 0x16c + 0x4,
        // isRunningSpotanim: 0x2c,
    },
    SpotAnimation: {
        id: 0x4,
        frame: 0x14,
    },

    GroundObject: {
        tag: 0x30,
    },
    WallDecorationObject: {
        tag: 0x50, //
    },
    StandardGameObject: {
        tag: 0xd8, //
    },
    WallObject: {
        tag: 0x30, //
    },

    EngineSettings: {
        fpsCap: 0x185e0,
    },

    GrandExchangeOffer: {
        status: 0x0,
        itemId: 0x04,
        amount: 0x0c,
        price: 0x08,
        isBuying: 0x0c,
    },
    GroundItem: {
        id: 0x18,
        amount: 0x1c,
    },
    RsItemContainer: {
        id: 0x0,
        itemIds: 0x8,
        itemAmounts: 0x20,
    },
    Menu: {
        isOpen: 0x3a0,
    },
    Npc: {
        composite: 0x550,
        index: 0x558,
        renderCycle: 0x120,
    },
    Player: {
        username: 0x578,//
        index: 0x5f0, //
        headIconPrayer: 0x554, //
        headIconSkull: 0x550, // 
        combatLevel: 0x5a0, //
    },

    NpcComposite: {
        id: 0x0,
    },

    Username: {
        value: 0x0,
    },
    Widget: {
        id: 0x24,
        index: 0x28,
        parentId: 0x94, 
        cycle: 0x58, 
        contentType: 0x50,
        text: 0x178, 
        itemId: 0x7a8 + 0x20, 
        itemAmount: 0x7ac + 0x20,
        width: 0x74, 
        height: 0x78, 
        relativeX: 0x7c, 
        relativeY: 0x80, 
        isHidden: 0x98, 
        textureId: 0xe8, 
        isInput: 0x5a0, 
        children: 0x7c8 + 0x10, 
    },

    ClientFingerprint: {
        libArchitecture: 0x0,
        processorArchitecture: 0x18,
        clientType: 0x50,
        deviceInfo: 0x68,
    },
    DeviceInfo: {
        deviceName: 0x0,
    },

    Native: {
        AInputEvent_getType: 0x00086850,
        AInputEvent_getDeviceId: 0x000877f0,

        AKeyEvent_getKeyCode: 0x00086840,
        AKeyEvent_getAction: 0x000877d0,
        AKeyEvent_getMetaState: 0x000877e0,

        AMotionEvent_getAction: 0x00095270,
        AMotionEvent_getPointerId: 0x000957f0,
        AMotionEvent_getX: 0x00094e20,
        AMotionEvent_getY: 0x00095200,
        AMotionEvent_getButtonState: 0x00094bf0,
        AMotionEvent_getPointerCount: 0x000943e0,
        AMotionEvent_getToolType: 0x00095540,
        AMotionEvent_getAxisValue: 0x000944b0,

        onInputEvent: 0xd8700,

        keyEvent_Action: 0x38,
        keyEvent_Flags: 0x3c,
        keyEvent_KeyCode: 0x40,
        keyEvent_ScanCode: 0x44,
        keyEvent_MetaState: 0x48,
        keyEvent_DownTime: 0x50,
        keyEvent_EventTime: 0x58,

        inputEvent_DeviceId: 0x4c,
        inputEvent_Type: 0x10,
        keyEvent_RepeatCount: 0x4c,
        inputEvent_Source: 0x10,
    },
}
